# Sonos Sub Dynamic Group Management Setup Guide

## ⚠️ IMPORTANT DISCOVERY
**Sonos Subs do NOT appear as separate entities in Home Assistant.** They are part of "bonded configurations" that show up as single zones/entities. This means we cannot directly move a Sub between groups - instead, we need alternative approaches.

## Updated Technical Reality
- **Sonos Subs are bonded** to their parent speakers (Arc, Amp, etc.)
- **No separate Sub entity** exists in Home Assistant
- **Sub follows its bonded group** automatically
- **Alternative solutions required** for your use case

## Alternative Approaches Available ✅
1. **Group Management**: Join/unjoin entire speaker groups
2. **Sub Control Attributes**: Use Sub enable/disable controls (if available)
3. **Smart Priority Management**: Intelligent group switching
4. **Physical Reconfiguration**: Manual Sonos app changes (not automated)

## Prerequisites

### 1. Discovery Phase (CRITICAL FIRST STEP)
**Before implementing any solution, you MUST run the discovery script** to understand your actual Sonos setup:

1. Use the provided `sonos_discovery_script.yaml`
2. Run all discovery scripts in Developer Tools > Services
3. Check Home Assistant logs for detailed output
4. Identify your actual entities and capabilities

### 2. Home Assistant Sonos Integration
Ensure the Sonos integration is properly configured:
```yaml
# Usually auto-discovered, but manual config if needed:
sonos:
  media_player:
    hosts:
      - *************  # Replace with your Sonos speaker IPs
      - *************
      - *************
```

### 3. Network Requirements
- Sonos devices must reach Home Assistant on TCP port 1400
- For Docker deployments, ensure proper network configuration
- Consider using `--net=host` for Docker if experiencing connectivity issues

### 4. Entity Identification
Find your actual Sonos entity names in Home Assistant:
1. Go to **Settings** > **Devices & Services** > **Sonos**
2. Note down the entity IDs for:
   - Main surround speaker (coordinator) - this includes the bonded Sub
   - Stereo pair speakers (left and right)
   - Any Sub-related control entities (switches, numbers)

## Installation Steps

### Step 1: Run Discovery (MANDATORY)
1. **Install Discovery Script**:
   - Copy `sonos_discovery_script.yaml` to your Home Assistant config
   - Restart Home Assistant or reload scripts

2. **Run Discovery**:
   - Go to Developer Tools > Services
   - Run each script: `script.discover_sonos_setup`, `script.check_sub_controls`, etc.
   - Check Home Assistant logs for detailed output

3. **Analyze Results**:
   - Identify your actual Sonos entities
   - Look for any Sub-related control entities
   - Understand your current group configurations

### Step 2: Choose Your Solution
Based on discovery results, choose from `sonos_alternative_solutions.yaml`:

#### Solution 1: Group Management (Most Practical)
- Joins stereo speakers to surround group when stereo plays
- Sub benefits from being in the larger group
- Separates groups when stereo stops

#### Solution 2: Sub Control Attributes (If Available)
- Uses Sub enable/disable switches
- Only works if your setup has Sub control entities

#### Solution 3: Smart Priority Management (Recommended)
- Intelligent group switching based on usage
- Prioritizes surround system for best experience
- Falls back to group joining for stereo

### Step 3: Update Entity Names
Edit your chosen solution and replace placeholder entity names:
```yaml
# REPLACE THESE WITH YOUR ACTUAL ENTITY NAMES FROM DISCOVERY:
media_player.sonos_surround_main    # Your 5.1 main speaker (includes bonded Sub)
media_player.sonos_stereo_left      # Left speaker of stereo pair
media_player.sonos_stereo_right     # Right speaker of stereo pair
# Note: No separate Sub entity - it's bonded to the surround main speaker
```

### Step 4: Test the Setup

#### Initial Testing
1. **Test Manual Scripts First**:
   - Run `script.sonos_join_stereo_to_surround`
   - Verify stereo speakers join surround group
   - Run `script.sonos_separate_stereo_from_surround`
   - Verify stereo speakers separate from surround group

2. **Test Automatic Triggers**:
   - Start playing music on stereo pair
   - Verify automation behavior based on chosen solution
   - Start playing on surround system
   - Verify priority handling works correctly

## Automation Logic

### Automation 1: Move Sub to Stereo
**Trigger**: Stereo speakers start playing
**Conditions**:
- Stereo group is actually playing
- Sub is not already in stereo group
- Surround system is not actively playing
**Action**: Unjoin Sub, then join to stereo group

### Automation 2: Return Sub to Surround
**Trigger**: Stereo speakers stop playing (30-second delay)
**Conditions**:
- Both stereo speakers are not playing
- Sub is currently in stereo group
**Action**: Unjoin Sub, then join to surround group

### Automation 3: Surround Priority Override
**Trigger**: Surround system starts playing
**Conditions**:
- Sub is not already in surround group
**Action**: Immediately move Sub to surround group

## Edge Case Handling

### Multiple Music Sources
- **Priority System**: Surround system takes priority over stereo
- **Delay Logic**: 30-second delay before returning Sub to prevent rapid switching
- **State Verification**: Checks actual playback state, not just entity state changes

### Overlapping Playback
- Surround system automatically reclaims Sub when it starts playing
- Prevents conflicts between simultaneous playback on both systems

### Network Issues
- Single mode prevents multiple automation instances
- Delay between unjoin and join operations for stability

## Troubleshooting

### Common Issues

1. **Sub Not Moving Between Groups**
   - Check entity names are correct
   - Verify Sonos integration is working
   - Check Home Assistant logs for errors

2. **Rapid Switching**
   - Increase delay timers if needed
   - Check for conflicting automations

3. **Docker Network Issues**
   - Ensure Sonos can reach Home Assistant on port 1400
   - Consider using `--net=host` for Docker

### Debug Commands
```yaml
# Check current group membership
{{ state_attr('media_player.sonos_sub', 'group_members') }}

# Check speaker states
{{ states('media_player.sonos_stereo_left') }}
{{ states('media_player.sonos_surround_main') }}
```

## Optional Enhancements

### Dashboard Controls
Add manual control buttons to your dashboard:
```yaml
# In your dashboard YAML
type: entities
entities:
  - entity: script.sonos_sub_to_stereo_manual
    name: "Move Sub to Stereo"
  - entity: script.sonos_sub_to_surround_manual
    name: "Move Sub to Surround"
```

### Notifications
Add notifications when Sub moves:
```yaml
# Add to automation actions
- service: notify.mobile_app_your_phone
  data:
    message: "Sonos Sub moved to {{ 'stereo' if 'stereo' in trigger.entity_id else 'surround' }} group"
```

### Advanced Conditions
Consider adding time-based conditions:
```yaml
# Only during certain hours
- condition: time
  after: "08:00:00"
  before: "22:00:00"
```

## Security Considerations
- This automation only uses standard Home Assistant media player services
- No external network access required
- All operations are local to your network

## Performance Impact
- Minimal CPU usage
- Network traffic only during group changes
- No continuous polling required

## Support
If you encounter issues:
1. Check Home Assistant logs
2. Verify Sonos integration status
3. Test manual scripts first
4. Consider posting in Home Assistant Community forums with specific error messages
