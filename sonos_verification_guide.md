# Sonos Sub Management - Verification & Troubleshooting Guide

## Step 1: Discovery Phase

### Run the Discovery Script
1. Copy `sonos_discovery_script.yaml` to your Home Assistant config
2. Restart Home Assistant or reload scripts
3. Go to **Developer Tools** > **Services**
4. Run each script one by one:
   - `script.discover_sonos_setup`
   - `script.check_sonos_services`
   - `script.check_sub_controls`
   - `script.test_group_detection`
   - `script.check_sub_attributes`

### Check the Logs
1. Go to **Settings** > **System** > **Logs**
2. Look for entries starting with "=== SONOS DISCOVERY REPORT ==="
3. Copy all the discovery output for analysis

### What to Look For

#### Expected Findings:
```
Sonos Media Players Found:
media_player.living_room (playing)
media_player.kitchen (idle)
media_player.surround_main (idle)
```

#### Group Membership Example:
```
media_player.surround_main:
- Group Members: ['media_player.surround_main', 'media_player.surround_left', 'media_player.surround_right']
- Is Coordinator: True
- Group Size: 3
```

#### Sub Control Entities (if they exist):
```
Switch Entities (Sub Controls):
switch.surround_main_subwoofer_enabled - on
number.surround_main_subwoofer_gain - 5
```

## Step 2: Understanding Your Setup

### Scenario A: No Separate Sub Entity (Most Common)
**What this means:**
- Your Sub is bonded to your main surround speaker
- It appears as part of the surround group
- You cannot move the Sub independently
- **Solution**: Use group management approach

**Recommended Solution:** Solution 1 or 4 from `sonos_alternative_solutions.yaml`

### Scenario B: Sub Control Entities Available
**What this means:**
- You have switch/number entities for Sub control
- You can enable/disable the Sub programmatically
- **Solution**: Use Sub control attributes approach

**Recommended Solution:** Solution 2 from `sonos_alternative_solutions.yaml`

### Scenario C: Multiple Physical Subs
**What this means:**
- You have separate Subs for each system
- Each Sub is bonded to its respective system
- **Solution**: Use smart priority management

**Recommended Solution:** Solution 4 from `sonos_alternative_solutions.yaml`

## Step 3: Implementation Verification

### Test Group Membership Detection
Run this in **Developer Tools** > **Template**:
```yaml
# Check current group memberships
Surround Group: {{ state_attr('media_player.YOUR_SURROUND_MAIN', 'group_members') }}
Stereo Left Group: {{ state_attr('media_player.YOUR_STEREO_LEFT', 'group_members') }}
Stereo Right Group: {{ state_attr('media_player.YOUR_STEREO_RIGHT', 'group_members') }}

# Check if groups are the same
Groups Match: {{ state_attr('media_player.YOUR_SURROUND_MAIN', 'group_members') == state_attr('media_player.YOUR_STEREO_LEFT', 'group_members') }}
```

### Test Manual Group Operations
1. **Join Test**:
   ```yaml
   service: media_player.join
   target:
     entity_id: media_player.YOUR_SURROUND_MAIN
   data:
     group_members:
       - media_player.YOUR_SURROUND_MAIN
       - media_player.YOUR_STEREO_LEFT
       - media_player.YOUR_STEREO_RIGHT
   ```

2. **Unjoin Test**:
   ```yaml
   service: media_player.unjoin
   target:
     entity_id: 
       - media_player.YOUR_STEREO_LEFT
       - media_player.YOUR_STEREO_RIGHT
   ```

## Step 4: Common Issues & Solutions

### Issue 1: "No Sub Entity Found"
**Symptoms:** Discovery script shows no `media_player.sonos_sub` entity
**Cause:** Subs are bonded to parent speakers, not separate entities
**Solution:** Use group management approach (Solution 1 or 4)

### Issue 2: "Groups Don't Join Properly"
**Symptoms:** Automation runs but speakers don't actually group
**Possible Causes:**
- Incorrect entity names
- Network connectivity issues
- Sonos speakers in different subnets

**Debugging Steps:**
1. Check entity names are exactly correct
2. Test manual join/unjoin in Developer Tools
3. Check Home Assistant logs for errors
4. Verify Sonos integration status

### Issue 3: "Music Stops When Grouping"
**Symptoms:** Music stops playing when automation runs
**Cause:** Incorrect group coordination
**Solution:** Ensure you're targeting the coordinator (first entity in group_members)

### Issue 4: "Sub Controls Not Available"
**Symptoms:** No switch/number entities for Sub control
**Cause:** Your Sonos setup doesn't expose Sub controls to Home Assistant
**Solution:** Use group management instead of Sub control approach

### Issue 5: "Docker Network Issues"
**Symptoms:** Sonos integration shows devices as unavailable
**Possible Solutions:**
- Use `--net=host` for Docker
- Ensure port 1400 is accessible
- Check firewall settings

## Step 5: Validation Tests

### Test 1: Basic Group Detection
```yaml
# Template to check if automation conditions would trigger
Stereo Playing: {{ states('media_player.YOUR_STEREO_LEFT') == 'playing' }}
Surround Playing: {{ states('media_player.YOUR_SURROUND_MAIN') == 'playing' }}
Groups Different: {{ state_attr('media_player.YOUR_STEREO_LEFT', 'group_members') != state_attr('media_player.YOUR_SURROUND_MAIN', 'group_members') }}
Should Join: {{ (states('media_player.YOUR_STEREO_LEFT') == 'playing') and (states('media_player.YOUR_SURROUND_MAIN') != 'playing') and (state_attr('media_player.YOUR_STEREO_LEFT', 'group_members') != state_attr('media_player.YOUR_SURROUND_MAIN', 'group_members')) }}
```

### Test 2: Sub Benefit Verification
1. Play music on stereo pair alone
2. Note bass response
3. Run join automation/script
4. Compare bass response (should be enhanced with Sub)

### Test 3: Priority System Verification
1. Start music on stereo pair
2. Verify it joins surround group
3. Start music on surround system
4. Verify stereo separates (if using priority solution)

## Step 6: Performance Monitoring

### Monitor Automation Execution
1. Go to **Settings** > **Automations & Scenes**
2. Click on your Sonos automation
3. Check "Last Triggered" and execution history
4. Look for any error messages

### Monitor Group Changes
Create a sensor to track group changes:
```yaml
template:
  - sensor:
      - name: "Sonos Group Status"
        state: >
          {% set surround = state_attr('media_player.YOUR_SURROUND_MAIN', 'group_members') | length %}
          {% set stereo = state_attr('media_player.YOUR_STEREO_LEFT', 'group_members') | length %}
          {% if surround > 3 %}
            Combined Group ({{ surround }} speakers)
          {% elif surround == 3 %}
            Surround Only ({{ surround }} speakers)
          {% else %}
            Separate Groups
          {% endif %}
```

## Step 7: Final Validation Checklist

- [ ] Discovery script completed successfully
- [ ] Actual entity names identified and updated in automation
- [ ] Manual join/unjoin scripts work correctly
- [ ] Automation triggers on correct conditions
- [ ] Music playback is not interrupted during group changes
- [ ] Sub effect is noticeable when groups are joined
- [ ] Priority system works as expected
- [ ] No errors in Home Assistant logs
- [ ] Performance is acceptable (no delays or glitches)

## Getting Help

If you're still having issues:

1. **Share Discovery Output**: Post the complete output from your discovery scripts
2. **Include Entity Names**: List your actual Sonos entity names
3. **Describe Behavior**: What happens vs. what you expect
4. **Check Logs**: Include any relevant error messages from Home Assistant logs
5. **Test Environment**: Mention your Home Assistant version and deployment method

## Alternative Approaches

If the automated solutions don't work for your setup:

1. **Manual Control**: Use the dashboard cards for manual group management
2. **Voice Control**: Set up voice commands for group switching
3. **Physical Setup**: Consider dedicated Subs for each system
4. **Sonos App**: Use native Sonos app for group management when needed
