# Alternative Sonos Sub Management Solutions
# Since Sonos Subs are NOT separate entities but part of bonded configurations,
# we need to work with entire speaker groups instead

# SOLUTION 1: Group-Based Management
# Move entire speaker groups instead of individual Sub
# This is the most practical approach for your use case

automation:
  # Automation 1: Join stereo speakers to surround group when stereo starts playing
  - id: sonos_stereo_to_surround_group
    alias: "Sonos: <PERSON><PERSON> to Surround Group"
    description: "Join stereo speakers to surround group when stereo starts playing"
    
    trigger:
      - platform: state
        entity_id:
          - media_player.sonos_stereo_left   # Replace with your actual entity
          - media_player.sonos_stereo_right  # Replace with your actual entity
        to: "playing"
        for:
          seconds: 3
    
    condition:
      - condition: and
        conditions:
          # Ensure stereo is actually playing
          - condition: or
            conditions:
              - condition: state
                entity_id: media_player.sonos_stereo_left
                state: "playing"
              - condition: state
                entity_id: media_player.sonos_stereo_right
                state: "playing"
          
          # Ensure surround system is not actively playing (priority check)
          - condition: not
            conditions:
              - condition: state
                entity_id: media_player.sonos_surround_main  # Replace with your actual entity
                state: "playing"
          
          # Ensure stereo speakers are not already in surround group
          - condition: template
            value_template: >
              {% set stereo_group = state_attr('media_player.sonos_stereo_left', 'group_members') %}
              {% set surround_group = state_attr('media_player.sonos_surround_main', 'group_members') %}
              {{ stereo_group != surround_group }}
    
    action:
      - variables:
          surround_group_members: "{{ state_attr('media_player.sonos_surround_main', 'group_members') }}"
          stereo_speakers: 
            - media_player.sonos_stereo_left
            - media_player.sonos_stereo_right
      
      - service: media_player.join
        target:
          entity_id: "{{ surround_group_members | first }}"
        data:
          group_members: "{{ surround_group_members + stereo_speakers }}"
    
    mode: single

  # Automation 2: Separate stereo from surround when stereo stops
  - id: sonos_separate_stereo_from_surround
    alias: "Sonos: Separate Stereo from Surround"
    description: "Separate stereo speakers from surround group when stereo stops playing"
    
    trigger:
      - platform: state
        entity_id:
          - media_player.sonos_stereo_left
          - media_player.sonos_stereo_right
        from: "playing"
        to:
          - "idle"
          - "paused"
          - "off"
        for:
          seconds: 30
    
    condition:
      - condition: and
        conditions:
          # Ensure both stereo speakers are not playing
          - condition: not
            conditions:
              - condition: state
                entity_id: media_player.sonos_stereo_left
                state: "playing"
          - condition: not
            conditions:
              - condition: state
                entity_id: media_player.sonos_stereo_right
                state: "playing"
          
          # Ensure stereo speakers are currently in a group with surround
          - condition: template
            value_template: >
              {% set stereo_group = state_attr('media_player.sonos_stereo_left', 'group_members') %}
              {% set surround_group = state_attr('media_player.sonos_surround_main', 'group_members') %}
              {{ stereo_group == surround_group and stereo_group|length > 1 }}
    
    action:
      - service: media_player.unjoin
        target:
          entity_id: 
            - media_player.sonos_stereo_left
            - media_player.sonos_stereo_right
    
    mode: single

# SOLUTION 2: Sub Control via Attributes
# Use the Sub-specific controls mentioned in Sonos integration documentation

  # Automation 3: Enable/Disable Sub based on active group
  - id: sonos_sub_control_by_group
    alias: "Sonos: Control Sub Based on Active Group"
    description: "Enable/disable subwoofer based on which group is playing"
    
    trigger:
      - platform: state
        entity_id:
          - media_player.sonos_stereo_left
          - media_player.sonos_surround_main
        to: "playing"
        for:
          seconds: 2
    
    action:
      - choose:
          # When stereo starts playing, disable sub on surround and enable on stereo
          - conditions:
              - condition: template
                value_template: "{{ 'stereo' in trigger.entity_id }}"
            sequence:
              # Disable sub on surround system (if it has sub controls)
              - service: switch.turn_off
                target:
                  entity_id: switch.sonos_surround_main_subwoofer_enabled
                continue_on_error: true
              
              # Enable sub on stereo system (if it has sub controls)
              - service: switch.turn_on
                target:
                  entity_id: switch.sonos_stereo_left_subwoofer_enabled
                continue_on_error: true
          
          # When surround starts playing, disable sub on stereo and enable on surround
          - conditions:
              - condition: template
                value_template: "{{ 'surround' in trigger.entity_id }}"
            sequence:
              # Disable sub on stereo system
              - service: switch.turn_off
                target:
                  entity_id: switch.sonos_stereo_left_subwoofer_enabled
                continue_on_error: true
              
              # Enable sub on surround system
              - service: switch.turn_on
                target:
                  entity_id: switch.sonos_surround_main_subwoofer_enabled
                continue_on_error: true
    
    mode: single

# SOLUTION 3: Physical Sub Movement (if you have multiple Subs)
# This assumes you have separate Subs that can be physically moved between groups

  # Automation 4: Move physical Sub between bonded configurations
  - id: sonos_physical_sub_management
    alias: "Sonos: Physical Sub Management"
    description: "Manage physical Sub between different bonded configurations"
    
    trigger:
      - platform: state
        entity_id:
          - media_player.sonos_stereo_left
          - media_player.sonos_surround_main
        to: "playing"
        for:
          seconds: 5
    
    action:
      - choose:
          # When stereo starts playing, use stereo configuration with Sub
          - conditions:
              - condition: template
                value_template: "{{ 'stereo' in trigger.entity_id }}"
              - condition: state
                entity_id: media_player.sonos_surround_main
                state: 
                  - "idle"
                  - "paused"
                  - "off"
            sequence:
              # This would require custom Sonos API calls or scripts
              # to reconfigure bonded speakers - this is complex and may not be practical
              - service: persistent_notification.create
                data:
                  title: "Sonos Sub Management"
                  message: "Stereo system active - Sub should be with stereo group"
          
          # When surround starts playing, use surround configuration with Sub
          - conditions:
              - condition: template
                value_template: "{{ 'surround' in trigger.entity_id }}"
            sequence:
              - service: persistent_notification.create
                data:
                  title: "Sonos Sub Management"
                  message: "Surround system active - Sub should be with surround group"
    
    mode: single

# SOLUTION 4: Smart Group Management with Priority
# Most practical solution - intelligently manage which group gets the "full experience"

  # Automation 5: Smart priority-based group management
  - id: sonos_smart_priority_management
    alias: "Sonos: Smart Priority Management"
    description: "Intelligently manage speaker groups based on usage patterns"
    
    trigger:
      - platform: state
        entity_id:
          - media_player.sonos_stereo_left
          - media_player.sonos_stereo_right
          - media_player.sonos_surround_main
        to: "playing"
        for:
          seconds: 3
    
    variables:
      stereo_playing: >
        {{ states('media_player.sonos_stereo_left') == 'playing' or 
           states('media_player.sonos_stereo_right') == 'playing' }}
      surround_playing: >
        {{ states('media_player.sonos_surround_main') == 'playing' }}
      
    action:
      - choose:
          # Priority 1: If surround starts playing, it gets priority
          - conditions:
              - condition: template
                value_template: "{{ 'surround' in trigger.entity_id }}"
            sequence:
              # Ensure surround group is isolated for best experience
              - service: media_player.unjoin
                target:
                  entity_id: 
                    - media_player.sonos_stereo_left
                    - media_player.sonos_stereo_right
                continue_on_error: true
              
              - service: persistent_notification.create
                data:
                  title: "Sonos Management"
                  message: "Surround system active - optimized for 5.1 experience with Sub"
          
          # Priority 2: If only stereo is playing, join to surround for Sub benefit
          - conditions:
              - condition: template
                value_template: "{{ 'stereo' in trigger.entity_id }}"
              - condition: template
                value_template: "{{ not surround_playing }}"
            sequence:
              - variables:
                  surround_group_members: "{{ state_attr('media_player.sonos_surround_main', 'group_members') }}"
                  stereo_speakers: 
                    - media_player.sonos_stereo_left
                    - media_player.sonos_stereo_right
              
              - service: media_player.join
                target:
                  entity_id: "{{ surround_group_members | first }}"
                data:
                  group_members: "{{ surround_group_members + stereo_speakers }}"
              
              - service: persistent_notification.create
                data:
                  title: "Sonos Management"
                  message: "Stereo joined to surround group - now has Sub support"
    
    mode: single

# Manual Scripts for Testing and Control
script:
  sonos_join_stereo_to_surround:
    alias: "Join Stereo to Surround Group"
    sequence:
      - variables:
          surround_group_members: "{{ state_attr('media_player.sonos_surround_main', 'group_members') }}"
          stereo_speakers: 
            - media_player.sonos_stereo_left
            - media_player.sonos_stereo_right
      
      - service: media_player.join
        target:
          entity_id: "{{ surround_group_members | first }}"
        data:
          group_members: "{{ surround_group_members + stereo_speakers }}"

  sonos_separate_stereo_from_surround:
    alias: "Separate Stereo from Surround"
    sequence:
      - service: media_player.unjoin
        target:
          entity_id: 
            - media_player.sonos_stereo_left
            - media_player.sonos_stereo_right

# IMPORTANT NOTES:
# 1. Replace all entity_id placeholders with your actual Sonos entity names
# 2. The Sub will follow whichever group it's bonded with
# 3. Solution 1 (Group-Based) is most practical and achievable
# 4. Solution 2 requires Sub control entities (may not exist in your setup)
# 5. Solution 3 is complex and may require Sonos app reconfiguration
# 6. Solution 4 provides intelligent priority management
# 7. Test with manual scripts first before enabling automations
