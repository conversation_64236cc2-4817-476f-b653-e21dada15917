# Sonos Discovery and Verification Script
# This script helps identify your actual Sonos entities and capabilities

# STEP 1: Run this script to discover your Sonos setup
# Go to Developer Tools > Services and run this script

script:
  discover_sonos_setup:
    alias: "Discover Sonos Setup"
    description: "Identify all Sonos entities and their capabilities"
    sequence:
      # Log all Sonos media player entities
      - service: system_log.write
        data:
          message: "=== SONOS DISCOVERY REPORT ==="
          level: info
      
      # Get all media player entities that contain 'sonos'
      - service: system_log.write
        data:
          message: >
            Sonos Media Players Found: 
            {% for entity in states.media_player %}
              {%- if 'sonos' in entity.entity_id.lower() -%}
                {{ entity.entity_id }} ({{ entity.state }})
              {%- endif -%}
            {% endfor %}
          level: info
      
      # Log detailed attributes for each Sonos entity
      - repeat:
          for_each: >
            {% set sonos_entities = [] %}
            {% for entity in states.media_player %}
              {%- if 'sonos' in entity.entity_id.lower() -%}
                {% set sonos_entities = sonos_entities + [entity.entity_id] %}
              {%- endif -%}
            {% endfor %}
            {{ sonos_entities }}
          sequence:
            - service: system_log.write
              data:
                message: >
                  === {{ repeat.item }} ===
                  State: {{ states(repeat.item) }}
                  Friendly Name: {{ state_attr(repeat.item, 'friendly_name') }}
                  Group Members: {{ state_attr(repeat.item, 'group_members') }}
                  Volume Level: {{ state_attr(repeat.item, 'volume_level') }}
                  Source: {{ state_attr(repeat.item, 'source') }}
                  Media Title: {{ state_attr(repeat.item, 'media_title') }}
                  Supported Features: {{ state_attr(repeat.item, 'supported_features') }}
                  Device Class: {{ state_attr(repeat.item, 'device_class') }}
                  All Attributes: {{ state_attr(repeat.item) }}
                level: info

# STEP 2: Alternative approach - Check for Sonos-specific services
  check_sonos_services:
    alias: "Check Available Sonos Services"
    description: "List all available Sonos-specific services"
    sequence:
      - service: system_log.write
        data:
          message: >
            Available Sonos Services:
            {% for domain in states | map(attribute='domain') | unique | list %}
              {%- if 'sonos' in domain.lower() -%}
                {{ domain }}
              {%- endif -%}
            {% endfor %}
          level: info

# STEP 3: Check for Sub-specific controls mentioned in documentation
  check_sub_controls:
    alias: "Check for Sub Controls"
    description: "Look for subwoofer-related entities and controls"
    sequence:
      - service: system_log.write
        data:
          message: "=== CHECKING FOR SUB CONTROLS ==="
          level: info
      
      # Check for number entities (Sub controls)
      - service: system_log.write
        data:
          message: >
            Number Entities (Sub Controls):
            {% for entity in states.number %}
              {%- if 'sonos' in entity.entity_id.lower() and ('sub' in entity.entity_id.lower() or 'bass' in entity.entity_id.lower()) -%}
                {{ entity.entity_id }} - {{ entity.state }} ({{ state_attr(entity.entity_id, 'friendly_name') }})
              {%- endif -%}
            {% endfor %}
          level: info
      
      # Check for switch entities (Sub enabled/disabled)
      - service: system_log.write
        data:
          message: >
            Switch Entities (Sub Controls):
            {% for entity in states.switch %}
              {%- if 'sonos' in entity.entity_id.lower() and 'sub' in entity.entity_id.lower() -%}
                {{ entity.entity_id }} - {{ entity.state }} ({{ state_attr(entity.entity_id, 'friendly_name') }})
              {%- endif -%}
            {% endfor %}
          level: info
      
      # Check for select entities
      - service: system_log.write
        data:
          message: >
            Select Entities (Sonos):
            {% for entity in states.select %}
              {%- if 'sonos' in entity.entity_id.lower() -%}
                {{ entity.entity_id }} - {{ entity.state }} ({{ state_attr(entity.entity_id, 'friendly_name') }})
              {%- endif -%}
            {% endfor %}
          level: info

# STEP 4: Test group membership detection
  test_group_detection:
    alias: "Test Group Membership Detection"
    description: "Test how to detect which speakers are in which groups"
    sequence:
      - service: system_log.write
        data:
          message: "=== GROUP MEMBERSHIP ANALYSIS ==="
          level: info
      
      - repeat:
          for_each: >
            {% set sonos_entities = [] %}
            {% for entity in states.media_player %}
              {%- if 'sonos' in entity.entity_id.lower() -%}
                {% set sonos_entities = sonos_entities + [entity.entity_id] %}
              {%- endif -%}
            {% endfor %}
            {{ sonos_entities }}
          sequence:
            - service: system_log.write
              data:
                message: >
                  {{ repeat.item }}:
                  - Group Members: {{ state_attr(repeat.item, 'group_members') | list }}
                  - Is Coordinator: {{ state_attr(repeat.item, 'group_members')[0] == repeat.item if state_attr(repeat.item, 'group_members') else 'Unknown' }}
                  - Group Size: {{ state_attr(repeat.item, 'group_members') | length if state_attr(repeat.item, 'group_members') else 0 }}
                level: info

# STEP 5: Check for any Sub-related attributes in main speakers
  check_sub_attributes:
    alias: "Check for Sub Attributes"
    description: "Look for subwoofer-related attributes in main speaker entities"
    sequence:
      - service: system_log.write
        data:
          message: "=== SUB-RELATED ATTRIBUTES ==="
          level: info
      
      - repeat:
          for_each: >
            {% set sonos_entities = [] %}
            {% for entity in states.media_player %}
              {%- if 'sonos' in entity.entity_id.lower() -%}
                {% set sonos_entities = sonos_entities + [entity.entity_id] %}
              {%- endif -%}
            {% endfor %}
            {{ sonos_entities }}
          sequence:
            - service: system_log.write
              data:
                message: >
                  {{ repeat.item }} - Checking for Sub attributes:
                  {% set attrs = state_attr(repeat.item) %}
                  {% for key, value in attrs.items() %}
                    {%- if 'sub' in key.lower() or 'bass' in key.lower() or 'surround' in key.lower() -%}
                      - {{ key }}: {{ value }}
                    {%- endif -%}
                  {% endfor %}
                level: info

# INSTRUCTIONS FOR USE:
# 1. Copy this entire script to a new file in your Home Assistant config
# 2. Restart Home Assistant or reload scripts
# 3. Go to Developer Tools > Services
# 4. Run each script one by one:
#    - script.discover_sonos_setup
#    - script.check_sonos_services  
#    - script.check_sub_controls
#    - script.test_group_detection
#    - script.check_sub_attributes
# 5. Check your Home Assistant logs (Settings > System > Logs) for the output
# 6. Share the log output to get a proper solution for your setup
